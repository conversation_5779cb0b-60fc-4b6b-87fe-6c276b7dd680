[{"run_id": "", "actor_id": "", "doc_id": "24053", "content": "基于深度学习的特征匹配算法分享\n分享清单\n【内容】\n<table>\n<tr>\n<td>方法名称<br/></td><td>简要说明<br/></td><td>文章链接<br/></td><td>会议名称<br/></td><td>发表时间<br/></td></tr>\n<tr>\n<td>**SuperGlue**<br/></td><td>**一种基于注意力图神经网络的特征匹配方法：**通过自注意力和交叉注意力交替构建图结构来**增强特征显著性和可匹配性表达。**设计带有dustbin通道的软分配矩阵来处理非正常匹配，并通过最小化对数似然损失函数优化匹配结果，**从而让匹配关系满足实际约束，有效解决遮挡等问题。**<br/></td><td><br/></td><td>CVPR<br/></td><td>2020<br/></td></tr>\n<tr>\n<td>**LightGlue**<br/></td><td>**一种结合相对位置编码的注意力结构和动态评估匹配置信度机制的方法：**相对位置旋转编码能够**更频繁更显性的利用位置信息，**提前退出以及特征点剪枝机制，可以移除不可靠的特征点，**减少冗余计算，加快推理速度。**<br/></td><td><br/></td><td>ICCV<br/></td><td>2023<br/></td></tr>\n<tr>\n<td>**ResMatch**<br/></td><td>**一种聚焦解除特征纠缠的特征匹配：**分析空间特征和视觉特征与注意力任务的相关性，自注意力模块**补充位置相关性增强描述符特征的筛选和强化，**交叉注意力模块**补充视觉相关性增强描述符特征的一致性表达，**并结合KNN实现稀疏注意力机制。<br/></td><td><br/></td><td>AAAI<br/></td><td>2024<br/></td></tr>\n<tr>\n<td>**OminiGlue**<br/></td><td>**第一个以提高泛化能力为核心原理设计的可学习特征匹配器：**引入DINOV2预训练模型，用来提取基础广泛且具有跨域表达能力的视觉特征，以此特征构建稀疏图神经网络，并在稀疏连接上用关键点位置计算注意力权重，**一种新的角度分离位置信息和视觉信息的纠缠，并且降低对特征点检测和提取器的依赖，提高模型对不同域数据的泛化能力。**<br/></td><td><br/></td><td>CVPR<br/></td><td>2024<br/></td></tr>\n<tr>\n<td>**SemaGlue**<br/></td><td>**一种结合语义感知增强的特征匹配：**利用预训练语义模型和特征点检测模型提取语义特征、视觉特征以及特征点位置信息，**利用语义特征增强视觉的特征表征能力，弥补纯视觉特征的表征缺陷。**<br/></td><td><br/></td><td>AAAI<br/></td><td>2025<br/></td></tr>\n<tr>\n<td>**MambaGlue**<br/></td><td>**一种结合Mamba架构与注意力机制的特征匹配方法**，同时捕捉全局和局部上下文，显著提升特征表达能力。<br/></td><td><br/></td><td>ICRA<br/></td><td>2025<br/></td></tr>\n</table>", "user_id": "", "agent_id": "", "milvus_id": "*****************", "metadata_json": {"doc_id": "24053", "doc_type": "doc", "publish_time": "2025-07-23 20:28:52", "project_area": "015", "secrecy_level": "internal", "doc_url": "https://mi.feishu.cn/docx/D1tydfHhJorz4LxT2pYcU7qBnDg", "doc_name": "基于深度学习的特征匹配算法分享", "tm": "2025-07-23 21:51:39", "owner": "{\"account\":\"陈言\",\"avatar240\":\"https://internal-api-lark-file.f.mioffice.cn/static-resource/v1/a2949440-7aa4-47ba-ac29-129e1997148l~?image_size=240x240&cut_type=&quality=&format=png&sticker_format=.webp\",\"avatar72\":\"https://internal-api-lark-file.f.mioffice.cn/static-resource/v1/a2949440-7aa4-47ba-ac29-129e1997148l~?image_size=72x72&cut_type=&quality=&format=png&sticker_format=.webp\",\"createTime\":*************,\"dept\":\"手机部-硬件工程部-相机部-算法部-视频组\",\"email\":\"<EMAIL>\",\"hrStatus\":\"A\",\"id\":1174451,\"openId\":\"ou_b52802c2dbfd99483463a2dbf7ff08b5\",\"openIdNew\":\"ou_7d9f9c52f3b276fd8a0e05b72ed3daed\",\"password\":\"\",\"updateTime\":*************,\"username\":\"chenyan16\"}", "doc_group": "知识库文档", "deleted": "0", "create_time": "2025-07-21 15:09:08", "create_user": "chenyan16", "update_time": "2025-07-21 19:20:31", "update_user": "wangziyi5", "source": "ipd", "chunk_idx": 0, "n_chunk": 1}, "id": "*****************", "score": 0.0315136476426799}, {"user_id": "", "agent_id": "", "run_id": "", "actor_id": "", "doc_id": "23497", "content": "热熔胶点胶工艺0508.pptx\n幻灯片 18\n【内容】\n- 内部资料，请勿外传\n- Mark点特征的选择原则：\n-  A.对角原则：一对mark点离的尽量远，有位置的情况下最好放到斜对角的位置：\n- B.点胶特征一致原则 :\n- ① 第一优先选择：和点胶小平面一起成型的特征。 特征首先选 择圆形，通孔；做不了通孔就选择圆形盲孔，如果做成圆形盲孔，凹坑底面建议为镜 面光面，凹坑周围要做成麻面，具体VDI等级需要客户在图纸标明；孔径大小推荐2-3mm。\n-  ② 第二优先选择：如果是一体CNC成型的特征，建议使用和点胶面同夹位，同一把刀一起 成型的特征作为mark点，建议铣成通孔，孔的大小在2-3mm都可；\n-  ③ 第三优先选择：如果和点胶面一起成型的工位无法设置mark点，可以使用成型的时候的定位孔，但是这个定位孔正面不能被覆盖，周围1mm之内不能被泡棉或者石墨片覆盖遮挡，孔周围不能有太多毛刺或者拉伤导致外形异常；一体CNC成型的工艺，次选择也可以是工位的定位孔，前提是避免后续组装遮住这个孔，孔周围至少留1mm不能遮挡；", "milvus_id": "*****************", "metadata_json": {"doc_id": "23497", "doc_type": "file", "publish_time": "2025-07-28 20:21:19", "project_area": "008", "secrecy_level": "secret", "doc_url": "https://mi.feishu.cn/file/GlcxbuGRNo6VMxxxoxrcsUkvnag", "doc_name": "热熔胶点胶工艺0508.pptx", "tm": "2025-07-28 20:43:19", "owner": "{\"account\":\"李昭玥\",\"avatar240\":\"https://internal-api-lark-file.f.mioffice.cn/static-resource/v1/v2_65867b57-898d-487d-9d85-4f6402d85cbl~?image_size=240x240&cut_type=&quality=&format=png&sticker_format=.webp\",\"avatar72\":\"https://internal-api-lark-file.f.mioffice.cn/static-resource/v1/v2_65867b57-898d-487d-9d85-4f6402d85cbl~?image_size=72x72&cut_type=&quality=&format=png&sticker_format=.webp\",\"createTime\":*************,\"dept\":\"手机部-硬件工程部-硬件研发部-结构材料部-材料工艺部\",\"email\":\"<EMAIL>\",\"hrStatus\":\"A\",\"id\":1109711,\"openId\":\"ou_0a30f7b933ef19bca3eedb482896c8a7\",\"openIdNew\":\"ou_0b320a774e4172fb38476cf6328bcce9\",\"password\":\"\",\"updateTime\":*************,\"username\":\"lizhaoyue\"}", "doc_group": "知识库文档", "status": "0", "result": "", "deleted": "0", "create_time": "2025-07-22 16:31:16", "create_user": "l<PERSON><PERSON><PERSON><PERSON>", "update_time": "2025-07-22 16:31:19", "update_user": "l<PERSON><PERSON><PERSON><PERSON>", "source": "ipd", "chunk_idx": 0, "n_chunk": 1}, "id": "*****************", "score": 0.029551337359792925}, {"run_id": "", "actor_id": "", "doc_id": "19881", "content": "手机整机组装可制造性设计指导检查表DFM规范\n更新记录\n【内容】\n| 35 | 14 组件化（9条→11条）①. 新增主板组件化要求；②. 新增摄像头与支架组件化建议； | 刘成 | / | 2024/4/12~2024/6/26 |  |\n| 36 | 15 防错漏混（6条→11条）①. 新增镜片丝印孔大小防呆要求；②. 新增后壳点胶丝印要求； | 刘成 | / | 2024/4/12~2024/6/27 |  |\n| V1 | 更新模板 | 毕慧芳 | / |  |  |\n| 1中框设计：①第3条，增加不同材质金属/塑胶尺寸的点胶面尺寸公差②第10条，增加了CNC中框和MDA中框内长宽公差③第13条，增加屏幕背胶粘贴面平面度要求④第16条，增加：mark点数量≥4个；Mark与屏幕组屏基准面的距离15*15mm；Mark点位置度公差，±0.05mm⑤第18条，增加3D曲面屏CG长宽尺寸公差⑥第20条，增加屏幕侧出排线，中框穿孔出口倒角要求⑦第21条，中框背胶等辅料实物对TP FPC过孔的避让尺寸，由≥0.20mm改为≥0.40mm⑧第28条，增加RT面倒角尺寸要求，防止CNC金属中框RT面在组屏/电池盖过程出现掉漆问题⑨第30条，增加长边点胶面直线度标准⑩第31条，增加点胶台阶与听筒缝边缘尺寸要求 | 汤小荣 | / | 2025/2/27 |  |  |\n| 2电池盖及镜片：①第4条，镜片背胶宽度，分防水和非防水场景定义②第14条，增加粘尘背胶设计要求③第21~24条，增加不同材质电池盖与中框的设计间隙④第25条，增加电池盖组装吸盘要求 | 汤小荣 | / | 2025/2/27 |  |  |\n| 4螺钉设计①第2条，BOX、支架等零件过孔单边避让螺钉由≥0.10mm修改为≥0.15mm（P代数字系列试运行中）②第6条，锁螺丝套筒壁厚改为0.60mm③第12条，增加折叠机螺丝种类要求 | 汤小荣 | / | 2025/2/27 |  |  |\n| 5屏幕设计①第1条，增加案例②第9条，增加屏幕与中框间隙设计要求 | 汤小荣 | / | 2025/2/27 |  |  |\n| 6PCBA设计①第8条，增加组装方向定义②第14条，增加非弹性臂卡扣，扣合量要求③第16条，增加电池仓FPC背胶与电池仓挡墙的距离要求，增加“若屏幕FPC、主FPC或其它FPC走电池仓中的VC上，可以粘贴到VC上“，增加”BTB补强到电池仓挡墙的距离要求≥1.50mm“ | 汤小荣 | / | 2025/2/27 |  |  |\n| 7辅料及二维码设计①第5条，增加PMMA材质外漏闪光灯罩清洁方法②第6条，因LCD仓大面积辅料为组件化来料，删除大面积辅料粘贴，透气孔要求 | 汤小荣 | / | 2025/2/27 |  |  |\n| 8摄像头及支架设计①第1条，增加前摄定位特征设计要求②第2条，由于摄像头磁性要求较低，螺钉边缘距离摄像头(不含有保护支架的）边缘距离由≥2mm，改为≥1.20mm③第4条，增加前摄支架背胶粘贴方案④第11条，新增屏幕倒装前摄支架设计要求 | 汤小荣 | / | 2025/2/27 |  |  |\n| 9电池设计①第2条，电池头部：螺帽边缘与电池边缘距离由≥1.50mm修改为≥1.10mm②第14条，增加电池背胶面积设计规范③第15条，增加电池仓平面度设计规范④第16条，增加电池仓表面能要求 | 汤小荣 | / | 2025/2/27 |  |  |\n| 11同轴线设计①第4条，增加0.50mmCable线的折弯半径要求②第9条，增加0.50mm同轴线耐摩擦要求③第10条，删除Cable线冗余空间位置要求④第11条，增加接地环卡槽宽度尺寸⑤第12条，刷新MDA和塑胶中框线缆卡点尺寸要求⑥第14条，增加0.50mm线径线缆走中框侧面（电池仓）开槽设计 | 汤小荣 | / | 2025/2/27 |  |  |\n| 12擦胶设计①第4条，四曲擦胶盲区宽度由＜0.15mm改为＜0.25mm，修改擦胶盲区定义②第5条，增加”建议中壳取消等离子处理如果增加等离子清洗，需要上PRB评审”条款③第8条，增加点胶面镭雕要求 | 汤小荣 | / | 2025/2/27 |  |  |\n| 13贴膜设计①第4、5条，增加UV膜叠层结构②第7、8、9条，增加UV膜各层剥离力要求③第13~21条，增加AR膜设计要求 | 汤小荣 | / | 2025/2/27 |  |  |\n| 14点胶设计①第6条，灌胶孔开孔深度由≥1.50mm更新为≥1.30mm②第15条，③第16条，②第20~31条，增加围坝胶工艺相关设计要求 | 汤小荣 | / | 2025/2/27 |  |  |\n| 15IPX8防水设计①第10条，更新卡托顶针密封圈与中框密封干涉量建议设计值：0.06~0.12mm | 汤小荣 | / | 2025/2/27 |  |  |", "user_id": "", "agent_id": "", "milvus_id": "*****************", "metadata_json": {"doc_id": "19881", "doc_type": "sheet", "publish_time": "2025-03-20 10:44:07", "project_area": "", "secrecy_level": "内部", "doc_url": "https://mi.feishu.cn/wiki/XOW0wpH4uizs4ekc46YcGvf3n4f", "doc_name": "手机整机组装可制造性设计指导检查表DFM规范", "tm": "2025-07-13 12:11:55", "owner": "{\"account\":\"汤小荣\",\"avatar240\":\"https://internal-api-lark-file.f.mioffice.cn/static-resource/v1/v3_00ij_cd6b7de2-95df-4152-ae2c-f1a2d848fael~?image_size=240x240&cut_type=&quality=&format=png&sticker_format=.webp\",\"avatar72\":\"https://internal-api-lark-file.f.mioffice.cn/static-resource/v1/v3_00ij_cd6b7de2-95df-4152-ae2c-f1a2d848fael~?image_size=72x72&cut_type=&quality=&format=png&sticker_format=.webp\",\"createTime\":*************,\"dept\":\"手机部-智能制造部-工艺工程部-北京工艺部-北京工艺一组\",\"email\":\"<EMAIL>\",\"hrStatus\":\"A\",\"id\":1506367,\"openId\":\"ou_467137507eb63a1419a578501b1a18a1\",\"openIdNew\":\"ou_1322c5b0a3757d141f4a2ba0e68ea30d\",\"password\":\"\",\"updateTime\":*************,\"username\":\"tangxiaorong\"}", "doc_group": "流程文档", "deleted": "0", "create_time": "2025-03-13 09:48:51", "create_user": "tang<PERSON><PERSON><PERSON>", "update_time": "2025-06-30 20:41:03", "update_user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "ipd", "chunk_idx": 2, "n_chunk": 3}, "id": "*****************", "score": 0.029551337359792925}, {"user_id": "", "agent_id": "", "run_id": "", "actor_id": "", "doc_id": "25487", "content": "ISP_月度分享_final_20191101.pdf\nI :a search area of the alternate image\n**In-house alignment**\nStep1:Fast point detect Step2: Find the main direction\n【内容】\nHow to describe the feature points:\n$$\n\\tau\\left(p:A,B\\right):=\\left\\{\\begin{matrix}1&:p(A)\n$$\n![](_page_24_Picture_7.jpeg)\n![](_page_24_Picture_8.jpeg)\nThen ,we can use the hamming distance to measure the similarity of feature points\n![](_page_24_Figure_2.jpeg)", "milvus_id": "*****************", "metadata_json": {"doc_id": "25487", "Header 3": "**In-house alignment**", "Header 4": "Step1:Fast point detect Step2: Find the main direction", "doc_type": "file", "publish_time": "2025-07-30 16:21:43", "project_area": "015", "secrecy_level": "secret", "doc_url": "https://mi.feishu.cn/wiki/PuJIwrWnBiwavZkOaU9cjheHnCd", "doc_name": "ISP_月度分享_final_20191101.pdf", "tm": "2025-07-30 17:12:07", "owner": "{\"account\":\"陈梓柔\",\"avatar240\":\"https://internal-api-lark-file.f.mioffice.cn/static-resource/v1/v3_00le_df0b44c6-ba42-45f4-96af-aec22889468l~?image_size=240x240&cut_type=&quality=&format=png&sticker_format=.webp\",\"avatar72\":\"https://internal-api-lark-file.f.mioffice.cn/static-resource/v1/v3_00le_df0b44c6-ba42-45f4-96af-aec22889468l~?image_size=72x72&cut_type=&quality=&format=png&sticker_format=.webp\",\"createTime\":*************,\"dept\":\"集团信息技术部-研产供数字化部-中台产品组\",\"email\":\"<EMAIL>\",\"hrStatus\":\"A\",\"id\":1595455,\"openId\":\"ou_8c0065bd544406c942662e69f0092a20\",\"openIdNew\":\"ou_aa1a8ab049b05b774b75a0a6bb8a2299\",\"password\":\"\",\"updateTime\":*************,\"username\":\"chenzirou\"}", "doc_group": "知识库文档", "status": "0", "result": "", "deleted": "0", "create_time": "2025-07-24 15:16:29", "create_user": "<PERSON><PERSON><PERSON><PERSON>", "update_time": "2025-07-24 15:16:32", "update_user": "<PERSON><PERSON><PERSON><PERSON>", "source": "ipd", "chunk_idx": 3, "n_chunk": 5}, "id": "*****************", "score": 0.02928692699490662}, {"run_id": "", "actor_id": "", "doc_id": "24053", "content": "基于深度学习的特征匹配算法分享\n算法简介\n**【ResMatch】Residual Attention Learning for Local  Feature Matching**\n【内容】\n- **Motivation：**\n- 现有方法缺乏对注意机制如何用于特征匹配的见解，不关心位置特征和视觉特征的表征偏好，这种空间信息和视觉信息纠缠的方式不利于将注意力 focus 到相关特征上。\n- 对于 SuperGlue 而言，重建 N*N 的匹配项并在迭代匹配步骤中进行全量特征的聚合操作太昂贵了。\n- **ResMatch** **Architecture：**\n> [!TIP]\n> **一种聚焦解除特征纠缠的特征匹配：**分析空间特征和视觉特征与注意力任务的相关性，自注意力模块补充位置相关性**增强描述符特征的筛选和强化**，交叉注意力模块补充视觉相关性**增强描述符特征的一致性表达**，并结合 KNN 实现稀疏注意力机制。\n> ![](boxk4n9qeWSHCr4S0SjSq0mp0Be)\n> ![](boxk4qa9FM1igiCI8i1V6biXHMd)\n>\n- **Residual Attention Learning**\n![](boxk4CX2njQIr07jO3ZXDjEIUQe)\n![](boxk4EFubRNNrdPx4zOcYI7UAcc)\n- 自注意力机制关注图内特征点的空间相关性，具体哪些位置的特征点对目标特征点有特征聚合的贡献，**重点在于位置因素对描述符特征的筛选和强化**\n- 交叉注意力机制关注图间特征点的视觉相关性，具体哪些视觉特征可以匹配，**重点在于视觉因素为主导的描述符特征的一致性表达**\n- **Residual 的由来：**上述两个均表示为相似度矩阵，以残余注意力形式对原始注意力进行补充，原始注意力则与 SuperGlue 一致。\n- **Sparse Attention Learning**\n$$\n{i} = KNN(x_{i},S^{\\ast }),S^{\\ast}\\in\\left \\{ S^{P},S^{D} \\right \\}\n$$\n$$\nx_{i},index\\left [ M_{i} \\right ] )=QK[M_{i}]^{T}\n$$\n$$\n{i} = Softmax(S/\\sqrt{c} )V[M_{i}]\n$$\n- 每次都会对注意力机制得到的权重矩阵进行筛选，每个特征点只会建立 topk 个连接参与特征向量的更新，与 LightGlue 不同的是，它不会移除特征点，所以只做到了稀疏化特征聚合", "user_id": "", "agent_id": "", "milvus_id": "*****************", "metadata_json": {"doc_id": "24053", "Header 3": "**【ResMatch】Residual Attention Learning for Local  Feature Matching**", "doc_type": "doc", "publish_time": "2025-07-23 20:28:52", "project_area": "015", "secrecy_level": "internal", "doc_url": "https://mi.feishu.cn/docx/D1tydfHhJorz4LxT2pYcU7qBnDg", "doc_name": "基于深度学习的特征匹配算法分享", "tm": "2025-07-23 21:51:39", "owner": "{\"account\":\"陈言\",\"avatar240\":\"https://internal-api-lark-file.f.mioffice.cn/static-resource/v1/a2949440-7aa4-47ba-ac29-129e1997148l~?image_size=240x240&cut_type=&quality=&format=png&sticker_format=.webp\",\"avatar72\":\"https://internal-api-lark-file.f.mioffice.cn/static-resource/v1/a2949440-7aa4-47ba-ac29-129e1997148l~?image_size=72x72&cut_type=&quality=&format=png&sticker_format=.webp\",\"createTime\":*************,\"dept\":\"手机部-硬件工程部-相机部-算法部-视频组\",\"email\":\"<EMAIL>\",\"hrStatus\":\"A\",\"id\":1174451,\"openId\":\"ou_b52802c2dbfd99483463a2dbf7ff08b5\",\"openIdNew\":\"ou_7d9f9c52f3b276fd8a0e05b72ed3daed\",\"password\":\"\",\"updateTime\":*************,\"username\":\"chenyan16\"}", "doc_group": "知识库文档", "deleted": "0", "create_time": "2025-07-21 15:09:08", "create_user": "chenyan16", "update_time": "2025-07-21 19:20:31", "update_user": "wangziyi5", "source": "ipd", "chunk_idx": 2, "n_chunk": 6}, "id": "*****************", "score": 0.029030910609857977}]