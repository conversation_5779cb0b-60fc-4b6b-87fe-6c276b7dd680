#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间参数识别服务测试用例
"""
import sys
import os
import asyncio
import unittest
import json
import time
from datetime import datetime, timedelta
from loguru import logger

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载{env_type}环境配置: {env_path}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)
        print(f"已加载默认环境配置: {default_env_path}")
    else:
        print(f"警告: 环境配置文件不存在: {env_path} 或 {default_env_path}")

from config.logging_config import configure_logging
from services.time_para_retrival_service import TimeParameterService, recognize_time_parameters

# 配置日志
configure_logging()

class TestTimeParameterService(unittest.IsolatedAsyncioTestCase):
    """时间参数识别服务测试类"""
    
    def setUp(self):
        """测试前的设置"""
        self.service = TimeParameterService(model_id="qwen3_4b_instruct", request_id="test-request")
        self.current_date = datetime.now().strftime("%Y-%m-%d")
        logger.info(f"测试开始，当前日期: {self.current_date}")
    
    async def test_yesterday_recognition(self):
        """测试昨天的识别"""
        user_query = "昨天我都完成了那些任务？"
        result = await self.service.recognize_time_parameters(user_query)
        
        # 计算预期的昨天日期
        yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
        expected = {"tm": [yesterday, yesterday]}
        
        logger.info(f"测试昨天识别 - 输入: {user_query}")
        logger.info(f"测试昨天识别 - 结果: {result}")
        logger.info(f"测试昨天识别 - 期望: {expected}")
        
        self.assertIsNotNone(result)
        self.assertIn("tm", result)
        if result["tm"] is not None:
            self.assertEqual(len(result["tm"]), 2)
            self.assertEqual(result["tm"][0], yesterday)
            self.assertEqual(result["tm"][1], yesterday)
    
    async def test_today_recognition(self):
        """测试今天的识别"""
        user_query = "今天的工作安排是什么？"
        result = await self.service.recognize_time_parameters(user_query)
        
        expected = {"tm": [self.current_date, self.current_date]}
        
        logger.info(f"测试今天识别 - 输入: {user_query}")
        logger.info(f"测试今天识别 - 结果: {result}")
        logger.info(f"测试今天识别 - 期望: {expected}")
        
        self.assertIsNotNone(result)
        self.assertIn("tm", result)
        if result["tm"] is not None:
            self.assertEqual(len(result["tm"]), 2)
            self.assertEqual(result["tm"][0], self.current_date)
            self.assertEqual(result["tm"][1], self.current_date)
    
    async def test_tomorrow_recognition(self):
        """测试明天的识别"""
        user_query = "明天有什么计划？"
        result = await self.service.recognize_time_parameters(user_query)
        
        # 计算预期的明天日期
        tomorrow = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
        expected = {"tm": [tomorrow, tomorrow]}
        
        logger.info(f"测试明天识别 - 输入: {user_query}")
        logger.info(f"测试明天识别 - 结果: {result}")
        logger.info(f"测试明天识别 - 期望: {expected}")
        
        self.assertIsNotNone(result)
        self.assertIn("tm", result)
        if result["tm"] is not None:
            self.assertEqual(len(result["tm"]), 2)
            self.assertEqual(result["tm"][0], tomorrow)
            self.assertEqual(result["tm"][1], tomorrow)
    
    async def test_no_time_parameter(self):
        """测试没有时间参数的问题"""
        user_query = "什么是人工智能？"
        result = await self.service.recognize_time_parameters(user_query)
        
        logger.info(f"测试无时间参数 - 输入: {user_query}")
        logger.info(f"测试无时间参数 - 结果: {result}")
        
        self.assertIsNotNone(result)
        self.assertIn("tm", result)
        self.assertIsNone(result["tm"])
    
    async def test_recent_days_recognition(self):
        """测试最近几天的识别"""
        user_query = "最近3天的数据分析报告"
        result = await self.service.recognize_time_parameters(user_query)
        
        # 计算预期的日期范围（最近3天：从3天前到今天）
        start_date = (datetime.now() - timedelta(days=2)).strftime("%Y-%m-%d")  # 3天前到今天，实际是3天
        end_date = self.current_date
        
        logger.info(f"测试最近几天识别 - 输入: {user_query}")
        logger.info(f"测试最近几天识别 - 结果: {result}")
        logger.info(f"测试最近几天识别 - 期望范围: {start_date} 到 {end_date}")
        
        self.assertIsNotNone(result)
        self.assertIn("tm", result)
        if result["tm"] is not None:
            self.assertEqual(len(result["tm"]), 2)
            # 由于模型可能有不同的理解，我们检查是否返回了合理的日期范围
            self.assertIsInstance(result["tm"][0], str)
            self.assertIsInstance(result["tm"][1], str)
    
    async def test_this_week_recognition(self):
        """测试本周的识别"""
        user_query = "本周的工作进展如何？"
        result = await self.service.recognize_time_parameters(user_query)
        
        logger.info(f"测试本周识别 - 输入: {user_query}")
        logger.info(f"测试本周识别 - 结果: {result}")
        
        self.assertIsNotNone(result)
        self.assertIn("tm", result)
        if result["tm"] is not None:
            self.assertEqual(len(result["tm"]), 2)
            # 检查返回的是否为有效日期格式
            self.assertIsInstance(result["tm"][0], str)
            self.assertIsInstance(result["tm"][1], str)
    
    async def test_specific_date_recognition(self):
        """测试具体日期的识别"""
        user_query = "2025-09-01的会议记录"
        result = await self.service.recognize_time_parameters(user_query)
        
        expected = {"tm": ["2025-09-01", "2025-09-01"]}
        
        logger.info(f"测试具体日期识别 - 输入: {user_query}")
        logger.info(f"测试具体日期识别 - 结果: {result}")
        logger.info(f"测试具体日期识别 - 期望: {expected}")
        
        self.assertIsNotNone(result)
        self.assertIn("tm", result)
        if result["tm"] is not None:
            self.assertEqual(len(result["tm"]), 2)
            # 检查是否包含指定的日期
            self.assertIn("2025-09-01", result["tm"])
    
    async def test_convenience_function(self):
        """测试便捷函数"""
        user_query = "昨天我都完成了那些任务？"
        result = await recognize_time_parameters(user_query, request_id="test-convenience")
        
        logger.info(f"测试便捷函数 - 输入: {user_query}")
        logger.info(f"测试便捷函数 - 结果: {result}")
        
        self.assertIsNotNone(result)
        self.assertIn("tm", result)
    
    def test_current_date_method(self):
        """测试获取当前日期的方法"""
        current_date = self.service._get_current_date()
        
        logger.info(f"测试当前日期方法 - 结果: {current_date}")
        
        self.assertIsNotNone(current_date)
        self.assertRegex(current_date, r'\d{4}-\d{2}-\d{2}')  # 检查日期格式
    
    def test_prompt_building(self):
        """测试提示词构建"""
        user_query = "昨天的任务完成情况"
        current_date = "2025-09-02"
        prompt = self.service._build_time_recognition_prompt(user_query, current_date)
        
        logger.info(f"测试提示词构建 - 用户查询: {user_query}")
        logger.info(f"测试提示词构建 - 当前日期: {current_date}")
        logger.info(f"测试提示词构建 - 生成的提示词长度: {len(prompt)}")
        
        self.assertIsNotNone(prompt)
        self.assertIn(user_query, prompt)
        self.assertIn(current_date, prompt)
        self.assertIn("时间参数识别助手", prompt)
    
    async def test_multiple_queries(self):
        """测试多个查询的批量处理"""
        test_queries = [
            "昨天我都完成了那些任务？",
            "今天的工作安排是什么？",
            "什么是人工智能？",
            "最近一周的数据统计",
            "明天的会议安排"
        ]

        results = []
        for query in test_queries:
            result = await self.service.recognize_time_parameters(query)
            results.append((query, result))
            logger.info(f"批量测试 - 查询: {query}, 结果: {result}")

        # 验证所有结果都有正确的格式
        for query, result in results:
            self.assertIsNotNone(result)
            self.assertIn("tm", result)
            if result["tm"] is not None:
                self.assertEqual(len(result["tm"]), 2)

    async def test_performance_timing(self):
        """测试性能和耗时"""
        test_queries = [
            "昨天我都完成了那些任务？",
            "今天的工作安排是什么？",
            "最近3天的数据分析报告"
        ]

        total_start_time = time.time()
        timings = []

        for query in test_queries:
            start_time = time.time()
            result = await self.service.recognize_time_parameters(query)
            end_time = time.time()
            duration = end_time - start_time

            timings.append({
                "query": query,
                "duration": duration,
                "result": result
            })

            logger.info(f"性能测试 - 查询: {query}")
            logger.info(f"性能测试 - 耗时: {duration:.3f}秒")
            logger.info(f"性能测试 - 结果: {result}")

            # 验证响应时间在合理范围内（通常应该在5秒内）
            self.assertLess(duration, 5.0, f"查询耗时过长: {duration:.3f}秒")

            # 验证结果格式正确
            self.assertIsNotNone(result)
            self.assertIn("tm", result)

        total_duration = time.time() - total_start_time
        avg_duration = sum(t["duration"] for t in timings) / len(timings)

        logger.info(f"性能测试总结:")
        logger.info(f"  - 总查询数: {len(test_queries)}")
        logger.info(f"  - 总耗时: {total_duration:.3f}秒")
        logger.info(f"  - 平均耗时: {avg_duration:.3f}秒")
        logger.info(f"  - 最快查询: {min(t['duration'] for t in timings):.3f}秒")
        logger.info(f"  - 最慢查询: {max(t['duration'] for t in timings):.3f}秒")

        # 验证平均响应时间在合理范围内
        self.assertLess(avg_duration, 3.0, f"平均响应时间过长: {avg_duration:.3f}秒")

    async def test_concurrent_requests(self):
        """测试并发请求性能"""
        test_queries = [
            "昨天我都完成了那些任务？",
            "今天的工作安排是什么？",
            "明天有什么计划？",
            "最近3天的数据分析",
            "本周的工作进展如何？"
        ]

        # 创建多个服务实例模拟并发
        services = [
            TimeParameterService(model_id="qwen3_4b_instruct", request_id=f"concurrent-test-{i}")
            for i in range(len(test_queries))
        ]

        start_time = time.time()

        # 并发执行所有查询
        tasks = [
            service.recognize_time_parameters(query)
            for service, query in zip(services, test_queries)
        ]

        results = await asyncio.gather(*tasks, return_exceptions=True)

        end_time = time.time()
        concurrent_duration = end_time - start_time

        logger.info(f"并发测试结果:")
        logger.info(f"  - 并发查询数: {len(test_queries)}")
        logger.info(f"  - 并发总耗时: {concurrent_duration:.3f}秒")
        logger.info(f"  - 平均每个查询耗时: {concurrent_duration/len(test_queries):.3f}秒")

        # 验证所有请求都成功完成
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.fail(f"并发请求 {i} 失败: {result}")
            else:
                self.assertIsNotNone(result)
                self.assertIn("tm", result)
                logger.info(f"  - 查询 {i+1}: {test_queries[i]} -> {result}")

        # 验证并发执行效率（应该比串行执行快）
        # 假设串行执行每个查询平均需要1秒，并发应该显著更快
        expected_serial_time = len(test_queries) * 1.0  # 假设每个查询1秒
        self.assertLess(concurrent_duration, expected_serial_time * 0.8,
                       f"并发执行效率不佳: {concurrent_duration:.3f}秒")

    async def test_response_time_consistency(self):
        """测试响应时间一致性"""
        query = "昨天我都完成了那些任务？"
        iterations = 3  # 减少迭代次数以避免测试时间过长
        timings = []

        logger.info(f"响应时间一致性测试 - 查询: {query}")
        logger.info(f"响应时间一致性测试 - 迭代次数: {iterations}")

        for i in range(iterations):
            start_time = time.time()
            result = await self.service.recognize_time_parameters(query)
            end_time = time.time()
            duration = end_time - start_time

            timings.append(duration)

            logger.info(f"  - 第 {i+1} 次: {duration:.3f}秒, 结果: {result}")

            # 验证每次都返回正确结果
            self.assertIsNotNone(result)
            self.assertIn("tm", result)

        # 计算统计信息
        avg_time = sum(timings) / len(timings)
        min_time = min(timings)
        max_time = max(timings)
        time_variance = sum((t - avg_time) ** 2 for t in timings) / len(timings)
        time_std = time_variance ** 0.5

        logger.info(f"响应时间统计:")
        logger.info(f"  - 平均时间: {avg_time:.3f}秒")
        logger.info(f"  - 最短时间: {min_time:.3f}秒")
        logger.info(f"  - 最长时间: {max_time:.3f}秒")
        logger.info(f"  - 标准差: {time_std:.3f}秒")
        logger.info(f"  - 变异系数: {(time_std/avg_time)*100:.1f}%")

        # 验证响应时间的一致性（标准差不应该太大）
        cv = time_std / avg_time  # 变异系数
        self.assertLess(cv, 0.5, f"响应时间变异过大: {cv:.3f}")

        # 验证最大最小时间差不会太大
        time_range = max_time - min_time
        self.assertLess(time_range, avg_time * 2, f"响应时间范围过大: {time_range:.3f}秒")


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
