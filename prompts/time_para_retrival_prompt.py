"""时间参数识别提示词模板"""

# 时间参数识别系统提示词
SYSTEM_PROMPT = """你是一个专业的时间参数识别助手。你的任务是从用户的问题中识别时间相关的表达，并将其转换为标准的日期格式。"""

# 时间参数识别主要提示词模板
TIME_RECOGNITION_PROMPT_TEMPLATE = """你是一个专业的时间参数识别助手。你的任务是从用户的问题中识别时间相关的表达，并将其转换为标准的日期格式。

当前日期：{current_date}

请分析用户的问题，识别其中的时间表达，并按照以下规则处理：

1. 时间表达识别规则：
   - "今天"：当前日期
   - "昨天"：当前日期的前一天
   - "明天"：当前日期的后一天
   - "前天"：当前日期的前两天
   - "后天"：当前日期的后两天
   - "本周"：本周的开始日期到结束日期
   - "上周"：上周的开始日期到结束日期
   - "下周"：下周的开始日期到结束日期
   - "本月"：本月的开始日期到结束日期
   - "上月"/"上个月"：上月的开始日期到结束日期
   - "下月"/"下个月"：下月的开始日期到结束日期
   - "最近X天"：从当前日期往前推X天到当前日期
   - "过去X天"：从当前日期往前推X天到当前日期
   - "未来X天"：从当前日期到往后推X天
   - 具体日期：如"2025-09-01"、"9月1日"等
   - 日期范围：如"9月1日到9月5日"等

2. 输出格式要求：
   - 如果识别到时间参数，返回JSON格式：{{"tm": ["开始日期", "结束日期"]}}
   - 日期格式统一为：YYYY-MM-DD
   - 如果是单个日期，开始日期和结束日期相同
   - 如果没有识别到时间参数，返回：{{"tm": null}}

3. 示例：
   - 输入："昨天我都完成了那些任务？" → {{"tm": ["2025-09-01", "2025-09-01"]}}（假设今天是2025-09-02）
   - 输入："本周的工作进展如何？" → {{"tm": ["2025-08-31", "2025-09-06"]}}（假设今天是2025-09-02，本周一是8月31日）
   - 输入："最近3天的数据分析" → {{"tm": ["2025-08-30", "2025-09-02"]}}（假设今天是2025-09-02）
   - 输入："什么是人工智能？" → {{"tm": null}}

用户问题：{user_query}

请仔细分析用户问题中的时间表达，并按照上述规则返回标准化的JSON格式结果。只返回JSON结果，不要包含其他解释文字。"""

# 时间参数识别用户查询模板
USER_QUERY_TEMPLATE = """{user_query}"""

# 时间表达规则说明
TIME_EXPRESSION_RULES = {
    "relative_days": {
        "今天": "current_date",
        "昨天": "current_date - 1 day",
        "明天": "current_date + 1 day",
        "前天": "current_date - 2 days",
        "后天": "current_date + 2 days"
    },
    "relative_weeks": {
        "本周": "current week start to end",
        "上周": "last week start to end",
        "下周": "next week start to end"
    },
    "relative_months": {
        "本月": "current month start to end",
        "上月": "last month start to end",
        "上个月": "last month start to end",
        "下月": "next month start to end",
        "下个月": "next month start to end"
    },
    "recent_days": {
        "最近X天": "from (current_date - X days) to current_date",
        "过去X天": "from (current_date - X days) to current_date",
        "未来X天": "from current_date to (current_date + X days)"
    },
    "absolute_dates": {
        "YYYY-MM-DD": "exact date format",
        "X月Y日": "month and day format",
        "X月Y日到Z月W日": "date range format"
    }
}

# 输出格式说明
OUTPUT_FORMAT = {
    "with_time": {
        "format": '{"tm": ["start_date", "end_date"]}',
        "date_format": "YYYY-MM-DD",
        "single_date": "start_date == end_date",
        "date_range": "start_date != end_date"
    },
    "without_time": {
        "format": '{"tm": null}'
    }
}

# 示例数据
EXAMPLES = [
    {
        "input": "昨天我都完成了那些任务？",
        "current_date": "2025-09-02",
        "output": '{"tm": ["2025-09-01", "2025-09-01"]}',
        "description": "昨天 - 单个日期"
    },
    {
        "input": "本周的工作进展如何？",
        "current_date": "2025-09-02",
        "output": '{"tm": ["2025-08-31", "2025-09-06"]}',
        "description": "本周 - 日期范围（假设周一是8月31日）"
    },
    {
        "input": "最近3天的数据分析",
        "current_date": "2025-09-02",
        "output": '{"tm": ["2025-08-30", "2025-09-02"]}',
        "description": "最近3天 - 日期范围"
    },
    {
        "input": "什么是人工智能？",
        "current_date": "2025-09-02",
        "output": '{"tm": null}',
        "description": "无时间参数"
    },
    {
        "input": "2025-09-01的会议记录",
        "current_date": "2025-09-02",
        "output": '{"tm": ["2025-09-01", "2025-09-01"]}',
        "description": "具体日期"
    }
]

def build_time_recognition_prompt(user_query: str, current_date: str) -> str:
    """
    构建时间识别的提示词
    
    Args:
        user_query: 用户输入的问题
        current_date: 当前日期，格式为YYYY-MM-DD
        
    Returns:
        str: 构建好的提示词
    """
    return TIME_RECOGNITION_PROMPT_TEMPLATE.format(
        user_query=user_query,
        current_date=current_date
    )

def get_system_prompt() -> str:
    """
    获取系统提示词
    
    Returns:
        str: 系统提示词
    """
    return SYSTEM_PROMPT

def get_user_query_template() -> str:
    """
    获取用户查询模板
    
    Returns:
        str: 用户查询模板
    """
    return USER_QUERY_TEMPLATE

def get_time_expression_rules() -> dict:
    """
    获取时间表达规则
    
    Returns:
        dict: 时间表达规则字典
    """
    return TIME_EXPRESSION_RULES

def get_output_format() -> dict:
    """
    获取输出格式说明
    
    Returns:
        dict: 输出格式说明
    """
    return OUTPUT_FORMAT

def get_examples() -> list:
    """
    获取示例数据
    
    Returns:
        list: 示例数据列表
    """
    return EXAMPLES
