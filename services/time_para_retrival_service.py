#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间参数识别服务
用于识别用户输入问题中的时间参数，并返回标准化的时间范围
"""
import sys
import os
import json
import re
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from loguru import logger

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.llm_provider import get_llm_provider
from config.logging_config import configure_logging
from prompts.time_para_retrival_prompt import build_time_recognition_prompt, get_system_prompt

configure_logging()

class TimeParameterService:
    """时间参数识别服务类"""
    
    def __init__(self, model_id: str = "qwen3_4b_instruct", request_id: str = None):
        """
        初始化时间参数识别服务
        
        Args:
            model_id: 使用的模型ID，默认为qwen3_4b_instruct
            request_id: 请求ID，用于日志追踪
        """
        self.model_id = model_id
        self.request_id = request_id
        self.logger = logger.bind(request_id=request_id)
        self.model_provider = None
        
    def _get_model_provider(self):
        """获取模型提供者"""
        if self.model_provider is None:
            # 对于qwen3_4b_instruct，不需要enable_thinking参数
            self.model_provider = get_llm_provider(self.model_id, self.request_id)
        return self.model_provider
    
    def _get_current_date(self) -> str:
        """获取当前日期，格式为YYYY-MM-DD"""
        return datetime.now().strftime("%Y-%m-%d")
    
    def _build_time_recognition_prompt(self, user_query: str, current_date: str) -> str:
        """
        构建时间识别的提示词

        Args:
            user_query: 用户输入的问题
            current_date: 当前日期

        Returns:
            str: 构建好的提示词
        """
        return build_time_recognition_prompt(user_query, current_date)
    
    async def extract_time_parameters(self, user_query: str) -> Dict[str, Any]:
        """
        从用户查询中提取时间参数

        Args:
            user_query: 用户输入的问题

        Returns:
            Dict: 包含时间参数的字典，格式为 {"tm": ["开始日期", "结束日期"]} 或 {"tm": null}
        """
        # 记录总体开始时间
        total_start_time = time.time()

        try:
            # 1. 获取当前日期
            date_start_time = time.time()
            current_date = self._get_current_date()
            date_duration = time.time() - date_start_time

            self.logger.info(f"开始识别时间参数，当前日期: {current_date}, 用户问题: {user_query}")
            self.logger.debug(f"获取当前日期耗时: {date_duration:.3f}秒")

            # 2. 构建提示词
            prompt_start_time = time.time()
            prompt = self._build_time_recognition_prompt(user_query, current_date)
            prompt_duration = time.time() - prompt_start_time
            self.logger.debug(f"构建提示词耗时: {prompt_duration:.3f}秒")

            # 3. 准备消息
            message_start_time = time.time()
            messages = [
                {
                    "role": "system",
                    "content": get_system_prompt()
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ]
            message_duration = time.time() - message_start_time
            self.logger.debug(f"准备消息耗时: {message_duration:.3f}秒")

            # 4. 获取模型提供者并生成响应
            model_start_time = time.time()
            model_provider = self._get_model_provider()
            model_init_duration = time.time() - model_start_time
            self.logger.debug(f"获取模型提供者耗时: {model_init_duration:.3f}秒")

            # 5. 调用模型生成响应
            generation_start_time = time.time()
            response = await model_provider.generate(messages=messages)
            generation_duration = time.time() - generation_start_time
            self.logger.info(f"模型生成响应耗时: {generation_duration:.3f}秒")

            if not response.get("success", False):
                self.logger.error(f"模型调用失败: {response}")
                return {"tm": None}

            # 6. 提取和解析响应内容
            parse_start_time = time.time()
            content = response.get("content", "").strip()
            self.logger.info(f"模型原始响应: {content}")

            # 尝试解析JSON响应
            try:
                # 使用正则表达式提取JSON部分
                json_match = re.search(r'\{.*\}', content)
                if json_match:
                    json_str = json_match.group()
                    result = json.loads(json_str)

                    # 验证结果格式
                    if "tm" in result:
                        parse_duration = time.time() - parse_start_time
                        total_duration = time.time() - total_start_time

                        self.logger.debug(f"解析响应耗时: {parse_duration:.3f}秒")
                        self.logger.info(f"时间参数识别总耗时: {total_duration:.3f}秒")
                        self.logger.info(f"成功识别时间参数: {result}")

                        # 记录详细的耗时分解
                        self.logger.debug(f"耗时分解 - 获取日期: {date_duration:.3f}s, "
                                        f"构建提示词: {prompt_duration:.3f}s, "
                                        f"准备消息: {message_duration:.3f}s, "
                                        f"模型初始化: {model_init_duration:.3f}s, "
                                        f"模型生成: {generation_duration:.3f}s, "
                                        f"解析响应: {parse_duration:.3f}s")

                        return result
                    else:
                        self.logger.warning(f"响应格式不正确，缺少tm字段: {result}")
                        return {"tm": None}
                else:
                    self.logger.warning(f"无法从响应中提取JSON: {content}")
                    return {"tm": None}

            except json.JSONDecodeError as e:
                self.logger.error(f"JSON解析失败: {e}, 原始内容: {content}")
                return {"tm": None}

        except Exception as e:
            total_duration = time.time() - total_start_time
            self.logger.error(f"时间参数识别过程中发生错误: {e}, 总耗时: {total_duration:.3f}秒")
            return {"tm": None}
    
    def _parse_relative_date(self, date_expr: str, current_date: str) -> List[str]:
        """
        解析相对日期表达（备用方法，用于模型无法正确识别时的后备处理）
        
        Args:
            date_expr: 日期表达式
            current_date: 当前日期
            
        Returns:
            List[str]: [开始日期, 结束日期]
        """
        current = datetime.strptime(current_date, "%Y-%m-%d")
        
        if "昨天" in date_expr:
            yesterday = current - timedelta(days=1)
            return [yesterday.strftime("%Y-%m-%d"), yesterday.strftime("%Y-%m-%d")]
        elif "今天" in date_expr:
            return [current_date, current_date]
        elif "明天" in date_expr:
            tomorrow = current + timedelta(days=1)
            return [tomorrow.strftime("%Y-%m-%d"), tomorrow.strftime("%Y-%m-%d")]
        elif "前天" in date_expr:
            day_before_yesterday = current - timedelta(days=2)
            return [day_before_yesterday.strftime("%Y-%m-%d"), day_before_yesterday.strftime("%Y-%m-%d")]
        elif "后天" in date_expr:
            day_after_tomorrow = current + timedelta(days=2)
            return [day_after_tomorrow.strftime("%Y-%m-%d"), day_after_tomorrow.strftime("%Y-%m-%d")]
        
        # 如果无法识别，返回None
        return None
    
    async def recognize_time_parameters(self, user_query: str) -> Dict[str, Any]:
        """
        识别时间参数的主要接口方法
        
        Args:
            user_query: 用户输入的问题
            
        Returns:
            Dict: 包含时间参数的字典
        """
        return await self.extract_time_parameters(user_query)


# 便捷函数
async def recognize_time_parameters(user_query: str, model_id: str = "qwen3_4b_instruct", request_id: str = None) -> Dict[str, Any]:
    """
    便捷函数：识别用户查询中的时间参数
    
    Args:
        user_query: 用户输入的问题
        model_id: 使用的模型ID
        request_id: 请求ID
        
    Returns:
        Dict: 包含时间参数的字典
    """
    service = TimeParameterService(model_id=model_id, request_id=request_id)
    return await service.recognize_time_parameters(user_query)
